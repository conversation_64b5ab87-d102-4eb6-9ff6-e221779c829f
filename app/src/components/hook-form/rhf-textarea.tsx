// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Textarea } from '../ui/textarea';

// ----------------------------------------------------------------------

type Props = {
	name: string;
	label?: string;
	placeholder?: string;
	rows?: number;
};

export function RHFTextarea({ name, label, placeholder, rows = 4 }: Props) {
	const { control } = useFormContext();

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => (
				<FormItem className='grid w-full items-center gap-1.5'>
					{label && <FormLabel className='font-normal'>{label}</FormLabel>}
					<FormControl>
						<Textarea placeholder={placeholder || ''} rows={rows} value={field.value} onChange={field.onChange} />
					</FormControl>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
