import * as React from 'react';
// utils
import { Slot } from '@radix-ui/react-slot';
import { Circle } from '@tools/reactor-icons';
import { cva, type VariantProps } from 'class-variance-authority';
// others
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------

const buttonVariants = cva(
	'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
	{
		variants: {
			variant: {
				default: 'bg-primary text-primary-foreground hover:bg-primary/90',
				neutral: 'bg-card text-red-500 text-foreground hover:bg-card/70',
				destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
				outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
				'outline-primary': 'border border-primary bg-transparent text-primary hover:bg-primary/10 ',
				secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
				ghost: 'hover:bg-accent hover:text-accent-foreground',
				link: 'text-primary underline-offset-4 hover:underline'
			},
			size: {
				default: 'h-9 px-4 py-2',
				sm: 'h-8 rounded-lg px-3 text',
				lg: 'h-10 rounded-lg px-8',
				icon: 'h-9 w-9 [&_svg]:size-auto',
				iconSm: 'h-7 w-7 rounded-lg [&_svg]:size-auto',
				roundIcon: 'h-8 w-8 rounded-xl'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	}
);

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {
	asChild?: boolean;
	isLoading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, isLoading, children, asChild = false, ...props }, ref) => {
		const Comp = asChild ? Slot : 'button';
		return (
			<Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props}>
				{isLoading ? <Circle className='animate-spin' /> : children}
			</Comp>
		);
	}
);
Button.displayName = 'Button';

export { Button, buttonVariants };
