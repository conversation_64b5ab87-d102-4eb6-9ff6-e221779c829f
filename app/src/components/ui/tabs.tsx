import * as React from 'react';
// utils
import * as TabsPrimitive from '@radix-ui/react-tabs';
import { cva, type VariantProps } from 'class-variance-authority';
// others
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------

const Tabs = TabsPrimitive.Root;

const tabsListVariants = cva('inline-flex items-center justify-center text-muted-foreground gap-2', {
	variants: {
		variant: {
			default: 'h-9 rounded-lg bg-[#F3F4FC] p-1 rounded-full',
			underline: 'h-auto bg-transparent p-0 w-fit'
		}
	},
	defaultVariants: {
		variant: 'default'
	}
});

const TabsList = React.forwardRef<
	React.ElementRef<typeof TabsPrimitive.List>,
	React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> & VariantProps<typeof tabsListVariants>
>(({ className, variant, ...props }, ref) => (
	<TabsPrimitive.List ref={ref} className={cn(tabsListVariants({ variant }), className)} {...props} />
));
TabsList.displayName = TabsPrimitive.List.displayName;

const tabsTriggerVariants = cva(
	'inline-flex outline-none shadow-none items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background outline-none transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
	{
		variants: {
			variant: {
				default:
					'rounded-md px-3 py-1 data-[state=active]:bg-card data-[state=active]:text-foreground data-[state=active]:shadow rounded-full',
				underline:
					'rounded-none px-1 py-2 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:font-semibold data-[state=active]:text-primary data-[state=active]:bg-transparent focus-visible:!ring-0 !ring-0 focus:outline-none focus:ring-0'
			}
		},
		defaultVariants: {
			variant: 'default'
		}
	}
);

const TabsTrigger = React.forwardRef<
	React.ElementRef<typeof TabsPrimitive.Trigger>,
	React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & VariantProps<typeof tabsTriggerVariants>
>(({ className, variant, ...props }, ref) => (
	<TabsPrimitive.Trigger ref={ref} className={cn(tabsTriggerVariants({ variant }), className)} {...props} />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
	React.ElementRef<typeof TabsPrimitive.Content>,
	React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
	<TabsPrimitive.Content
		ref={ref}
		className={cn(
			'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
			className
		)}
		{...props}
	/>
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
export type { VariantProps as TabsListVariantProps } from 'class-variance-authority';
export type { VariantProps as TabsTriggerVariantProps } from 'class-variance-authority';
