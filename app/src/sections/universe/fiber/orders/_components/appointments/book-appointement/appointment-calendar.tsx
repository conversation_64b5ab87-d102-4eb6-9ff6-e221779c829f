import { useState } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { add, format, startOfWeek, endOfWeek, eachDayOfInterval, isBefore, isAfter, isEqual, sub, isSameDay, isToday } from 'date-fns';
import { fr } from 'date-fns/locale';
import { twJoin } from 'tailwind-merge';
// api
import { queries } from '@/api/queries';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// components
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';

// ----------------------------------------------------------------------

function AppointmentCalendar() {
	const { order } = useOrder();

	const today = new Date();
	const testDate = new Date('2025-06-23'); // Start of week (Monday)
	const initialStartDate = startOfWeek(testDate, { weekStartsOn: 1 });
	const maxWeeksAhead = 5;

	const [currentWeekStart, setCurrentWeekStart] = useState(initialStartDate);
	const [selectedDate, setSelectedDate] = useState<string | null>(null);
	const [selectedTime, setSelectedTime] = useState<string | null>(null);

	const currentWeekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
	const weekDays = eachDayOfInterval({ start: currentWeekStart, end: currentWeekEnd });

	const mockAvailabilities = {
		'2025-06-23': {
			// Monday
			'8:00': 'HO single',
			'9:00': 'HO',
			'10:00': 'HNO single',
			'12:00': 'HO',
			'14:00': 'HNO',
			'16:00': 'HO single'
		},
		'2025-06-24': {
			// Tuesday
			'8:00': 'HO',
			'9:00': 'HO single',
			'10:00': 'HO',
			'11:00': 'HNO',
			'13:00': 'HO single',
			'14:00': 'HNO single',
			'15:00': 'HO',
			'16:00': 'HO single'
		},
		'2025-06-25': {
			// Wednesday
			'9:00': 'HO',
			'10:00': 'HO single',
			'11:00': 'HO',
			'12:00': 'HO single',
			'13:00': 'HNO',
			'14:00': 'HNO single',
			'15:00': 'HO'
		},
		'2025-06-26': {
			// Thursday
			'8:00': 'HO',
			'9:00': 'HO single',
			'10:00': 'HO',
			'11:00': 'HNO',
			'12:00': 'HO single',
			'13:00': 'HNO single',
			'14:00': 'HNO',
			'16:00': 'HO'
		},
		'2025-06-27': {
			// Friday
			'9:00': 'HO single',
			'10:00': 'HO',
			'11:00': 'HNO',
			'13:00': 'HO single',
			'14:00': 'HO',
			'15:00': 'HO single',
			'16:00': 'HO',
			'18:00': 'HO single'
		},
		'2025-06-28': {
			// Saturday
			'10:00': 'HO',
			'11:00': 'HO single',
			'13:00': 'HNO',
			'14:00': 'HNO single',
			'16:00': 'HO'
		},
		'2025-06-29': {
			// Sunday
			'13:00': 'HO single',
			'14:00': 'HO'
		}
	};

	const { data: apiAvailabilities, isLoading } = useQuery({
		...queries.fiber_order.byId(order?.['@id'])._ctx.planning({
			duration: 60,
			from: format(currentWeekStart, 'yyyy-MM-dd'),
			to: format(currentWeekEnd, 'yyyy-MM-dd')
		}),
		enabled: !!order?.['@id']
	});

	const isTestWeek = format(currentWeekStart, 'yyyy-MM-dd') === '2025-06-23';
	const availabilities = isTestWeek ? mockAvailabilities : apiAvailabilities || {};

	const handlePreviousWeek = () => {
		const newWeekStart = sub(currentWeekStart, { weeks: 1 });
		if (isAfter(newWeekStart, initialStartDate) || isEqual(newWeekStart, initialStartDate)) {
			setCurrentWeekStart(newWeekStart);
			// Clear selection when changing weeks
			setSelectedDate(null);
			setSelectedTime(null);
		}
	};

	const handleNextWeek = () => {
		const newWeekStart = add(currentWeekStart, { weeks: 1 });
		const maxDate = add(initialStartDate, { weeks: maxWeeksAhead });
		if (isBefore(newWeekStart, maxDate) || isEqual(newWeekStart, maxDate)) {
			setCurrentWeekStart(newWeekStart);
			setSelectedDate(null);
			setSelectedTime(null);
		}
	};

	const isPrevDisabled = isBefore(sub(currentWeekStart, { weeks: 1 }), initialStartDate);
	const isNextDisabled = isAfter(add(currentWeekStart, { weeks: 1 }), add(initialStartDate, { weeks: maxWeeksAhead }));

	const handleTimeSlotClick = (date: string, time: string) => {
		if (selectedDate === date && selectedTime === time) {
			setSelectedDate(null);
			setSelectedTime(null);
		} else {
			setSelectedDate(date);
			setSelectedTime(time);
		}
	};

	const getAllTimeSlots = (): string[] => {
		const standardTimeSlots = [
			'8:00',
			'9:00',
			'10:00',
			'11:00',
			'12:00',
			'13:00',
			'14:00',
			'15:00',
			'16:00',
			'17:00',
			'18:00',
			'19:00'
		];

		const allTimes = new Set<string>(standardTimeSlots);

		// Add any additional times from availabilities
		Object.values(availabilities || {}).forEach((daySlots) => {
			Object.keys(daySlots).forEach((time) => allTimes.add(time));
		});

		return Array.from(allTimes).sort((a, b) => {
			const parseTime = (timeStr: string) => {
				const [hours, minutes] = timeStr.split(':').map(Number);
				return hours * 60 + minutes;
			};
			return parseTime(a) - parseTime(b);
		});
	};

	const getSlotInfo = (date: Date, time: string): { exists: boolean; type: string; isPast: boolean } => {
		const dateKey = format(date, 'yyyy-MM-dd');
		const exists = !!availabilities?.[dateKey]?.[time];
		const type = availabilities?.[dateKey]?.[time] || 'available';
		const isPast = isTimeSlotPast(date, time);

		return { exists, type, isPast };
	};

	const allTimeSlots = getAllTimeSlots();

	const isTimeSlotPast = (date: Date, time: string): boolean => {
		const slotDateTime = new Date(`${format(date, 'yyyy-MM-dd')} ${time}`);
		return isBefore(slotDateTime, new Date());
	};

	return (
		<div className='w-full space-y-4'>
			<div className='flex items-start justify-between mt-6'>
				<div className='text-sm text-muted-foreground'>Plage de possibilités</div>

				<div className='flex items-center justify-center space-x-2'>
					<Button
						variant='ghost'
						size='sm'
						onClick={handlePreviousWeek}
						disabled={isPrevDisabled}
						className={twJoin(
							'text-primary hover:text-primary/80',
							isPrevDisabled && 'text-muted-foreground cursor-not-allowed'
						)}>
						<ChevronLeft className='h-4 w-4' />
					</Button>

					<div className='text-center min-w-[150px]'>
						<div className='text-sm font-semibold text-primary'>
							Semaine du {format(currentWeekStart, 'dd', { locale: fr })} au{' '}
							{format(currentWeekEnd, 'dd MMMM', { locale: fr })}
						</div>
					</div>

					<Button
						variant='ghost'
						size='sm'
						onClick={handleNextWeek}
						disabled={isNextDisabled}
						className={twJoin(
							'text-primary hover:text-primary/80',
							isNextDisabled && 'text-muted-foreground cursor-not-allowed'
						)}>
						<ChevronRight className='h-4 w-4' />
					</Button>
				</div>
			</div>
			<div className='w-full'>
				<div className='grid grid-cols-7 gap-2 mb-4'>
					{weekDays.map((day) => {
						const dayKey = format(day, 'yyyy-MM-dd');
						const isPastDay = isBefore(day, today) && !isSameDay(day, today);

						return (
							<div key={dayKey} className='text-center pb-2'>
								<div className='text-xs text-muted-foreground capitalize font-medium'></div>
								<div
									className={twJoin(
										'text-sm font-semibold mt-1 capitalize',
										isToday(day) && 'text-primary',
										isPastDay && 'text-muted-foreground'
									)}>
									{format(day, 'EEE', { locale: fr })} {format(day, 'dd/MM', { locale: fr })}
								</div>
							</div>
						);
					})}
				</div>

				{/* Check which days and time slots are in the past */}
				{(() => {
					const pastDays = weekDays.map((day) => isBefore(day, today) && !isSameDay(day, today));
					const hasPastDays = pastDays.some((isPast) => isPast);

					// Calculate past time slots for today
					const todayIndex = weekDays.findIndex((day) => isSameDay(day, today));
					const pastTimeSlots =
						todayIndex >= 0
							? allTimeSlots.filter((time) => {
									const slotDateTime = new Date(`${format(today, 'yyyy-MM-dd')} ${time}`);
									return isBefore(slotDateTime, today);
								})
							: [];

					return (
						<div className='relative'>
							{/* Past day overlays - merged rectangles for full past days */}
							{hasPastDays && (
								<div className='absolute inset-0 grid grid-cols-7 gap-3 pointer-events-none z-10'>
									{weekDays.map((day, dayIndex) => {
										const dayKey = format(day, 'yyyy-MM-dd');
										const isPastDay = pastDays[dayIndex];

										if (!isPastDay) {
											return <div key={dayKey} />;
										}

										return (
											<div
												key={dayKey}
												className='bg-gray-50 rounded-md'
												style={{ height: `${allTimeSlots.length * 52 - 15}px` }}
											/>
										);
									})}
								</div>
							)}

							{/* Past time slot overlays for today */}
							{todayIndex >= 0 && pastTimeSlots.length > 0 && (
								<div className='absolute inset-0 grid grid-cols-7 gap-3 pointer-events-none z-10'>
									{weekDays.map((day, dayIndex) => {
										const dayKey = format(day, 'yyyy-MM-dd');
										const isToday = dayIndex === todayIndex;

										if (!isToday) {
											return <div key={dayKey} />;
										}

										return (
											<div
												key={dayKey}
												className='bg-gray-50 rounded-md'
												style={{ height: `${pastTimeSlots.length * 52 - 14}px` }}
											/>
										);
									})}
								</div>
							)}

							{/* Regular time slots grid */}
							<div className='space-y-3'>
								{allTimeSlots.map((time) => {
									const isPastTimeSlot = pastTimeSlots.includes(time);

									return (
										<div key={time} className='grid grid-cols-7 gap-3'>
											{weekDays.map((day, dayIndex) => {
												const dayKey = format(day, 'yyyy-MM-dd');
												const slotInfo = getSlotInfo(day, time);
												const isSelected = selectedDate === dayKey && selectedTime === time;
												const isPastDay = pastDays[dayIndex];
												const isToday = dayIndex === todayIndex;
												const isPastSlot = isPastDay || (isToday && isPastTimeSlot);

												const getSlotColors = () => {
													if (isPastSlot) {
														return 'bg-transparent text-transparent'; // Hidden under overlay
													}
													if (!slotInfo.exists) {
														return `bg-gray-50 text-transparent hover:text-gray-400 hover:bg-gray-100 cursor-pointer ${isSelected ? ' border-gray-200 border-2 !text-gray-500' : ''}`; // Empty slots (clickable)
													}

													// New status types
													switch (slotInfo.type) {
														case 'HNO single':
															return `bg-orange-50 text-orange-900 hover:bg-orange-100 ${isSelected ? ' border-orange-800 border-2' : ''}`;
														case 'HNO':
															return `bg-orange-200 text-orange-900 hover:bg-orange-300 border-orange-800 ${isSelected ? ' border-orange-800 border-2' : ''}`;
														case 'HO single':
															return `bg-purple-50 text-purple-900 hover:bg-purple-100 ${isSelected ? ' border-purple-800 border-2' : ''}`;
														case 'HO':
															return `bg-purple-200 text-purple-900 hover:bg-purple-300' ${isSelected ? ' border-purple-800 border-2' : ''}`;
														default:
															return `bg-gray-100 text-gray-900 hover:bg-gray-200  ${isSelected ? ' border-gray-600 border-2' : ''}`;
													}
												};

												return (
													<div key={`${dayKey}-${time}`} className='h-10'>
														<button
															className={twJoin(
																'w-full h-full text-sm px-2 rounded-md font-semibold transition-all duration-200',
																getSlotColors()
															)}
															onClick={() => !isPastSlot && handleTimeSlotClick(dayKey, time)}
															disabled={isPastSlot}>
															{!isPastSlot ? time : ''}
														</button>
													</div>
												);
											})}
										</div>
									);
								})}
							</div>
						</div>
					);
				})()}
			</div>

			{isLoading && (
				<div className='text-center py-8'>
					<div className='text-sm text-muted-foreground'>Chargement des créneaux...</div>
				</div>
			)}

			{!isLoading && (!availabilities || Object.keys(availabilities).length === 0) && allTimeSlots.length === 0 && (
				<div className='flex items-center justify-center py-16 bg-gray-50 rounded-xl mt-4'>
					<div className='text-center space-y-4'>
						<Calendar className='h-8 w-8 text-muted-foreground mx-auto' />
						<div className='text-sm text-muted-foreground'>Aucun créneau disponible pour cette semaine</div>
						<Button variant='outline' size='sm' className='text-primary border-primary hover:bg-primary/10'>
							<Calendar className='h-4 w-4 mr-2' />
							Demande d'ouverture de planning
						</Button>
					</div>
				</div>
			)}

			{selectedDate && selectedTime && (
				<div className='bg-purple-50 border border-purple-200 rounded-lg p-3 mt-4'>
					<div className='text-sm font-medium text-purple-800'>
						Créneau sélectionné: {format(new Date(selectedDate), 'EEEE dd MMMM yyyy', { locale: fr })} à {selectedTime}
					</div>
				</div>
			)}
		</div>
	);
}

export default AppointmentCalendar;
