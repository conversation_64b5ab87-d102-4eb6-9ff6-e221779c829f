import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
// components
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/hook-form/form-provider';
import { RHFTextarea } from '@/components/hook-form/rhf-textarea';
import { Calendar, Clock, Timer, User } from 'lucide-react';

// ----------------------------------------------------------------------

const appointmentConfirmationSchema = z.object({
	comment: z.string().optional()
});

type AppointmentConfirmationData = z.infer<typeof appointmentConfirmationSchema>;

interface AppointmentData {
	date: string;
	time: string;
	type: string;
	duration: string;
	technician: string;
}

interface AppointmentConfirmationDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	appointmentData: AppointmentData | null;
	onConfirm: (data: AppointmentConfirmationData) => void;
}

function AppointmentConfirmationDialog({
	open,
	onOpenChange,
	appointmentData,
	onConfirm
}: AppointmentConfirmationDialogProps) {
	const methods = useForm<AppointmentConfirmationData>({
		resolver: zodResolver(appointmentConfirmationSchema),
		defaultValues: {
			comment: ''
		}
	});

	const { handleSubmit, reset } = methods;

	const onSubmit = (data: AppointmentConfirmationData) => {
		onConfirm(data);
		reset();
		onOpenChange(false);
	};

	const handleCancel = () => {
		reset();
		onOpenChange(false);
	};

	if (!appointmentData) return null;

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='max-w-md'>
				<DialogHeader>
					<DialogTitle>Rendez-vous</DialogTitle>
				</DialogHeader>

				<Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
					<div className='space-y-6'>
						{/* Appointment Summary */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Récapitulatif du RDV</h3>
							<div className='grid grid-cols-2 gap-4'>
								<div>
									<div className='flex items-center text-sm text-muted-foreground mb-1'>
										<User className='w-4 h-4 mr-2' />
										Type de rendez-vous
									</div>
									<div className='font-medium'>{appointmentData.type}</div>
								</div>

								<div>
									<div className='flex items-center text-sm text-muted-foreground mb-1'>
										<Calendar className='w-4 h-4 mr-2' />
										Date
									</div>
									<div className='font-medium'>
										{format(new Date(appointmentData.date), 'EEEE dd MMMM', { locale: fr })}
									</div>
								</div>

								<div>
									<div className='flex items-center text-sm text-muted-foreground mb-1'>
										<Clock className='w-4 h-4 mr-2' />
										Heure
									</div>
									<div className='font-medium'>{appointmentData.time}</div>
								</div>

								<div>
									<div className='flex items-center text-sm text-muted-foreground mb-1'>
										<Timer className='w-4 h-4 mr-2' />
										Durée du RDV
									</div>
									<div className='font-medium'>{appointmentData.duration}</div>
								</div>
							</div>

							<div className='mt-4'>
								<div className='flex items-center text-sm text-muted-foreground mb-1'>
									<User className='w-4 h-4 mr-2' />
									Technicien
								</div>
								<div className='font-medium'>{appointmentData.technician}</div>
								<div className='flex space-x-2 mt-2'>
									<Button variant='outline' size='sm' className='h-8 w-8 p-0'>
										<Calendar className='h-4 w-4' />
									</Button>
									<Button variant='outline' size='sm' className='h-8 w-8 p-0'>
										<span className='text-xs'>📄</span>
									</Button>
									<Button variant='outline' size='sm' className='h-8 w-8 p-0'>
										<span className='text-xs'>T</span>
									</Button>
									<Button variant='outline' size='sm' className='h-8 w-8 p-0'>
										<span className='text-xs'>📧</span>
									</Button>
								</div>
							</div>
						</div>

						{/* Comment Section */}
						<div>
							<RHFTextarea
								name='comment'
								label='Commentaire (facultatif)'
								placeholder='Ex : commentaire'
								rows={4}
							/>
						</div>
					</div>

					<DialogFooter className='mt-6'>
						<Button type='button' variant='outline' onClick={handleCancel}>
							Annuler
						</Button>
						<Button type='submit' className='bg-purple-600 hover:bg-purple-700'>
							Valider le RDV
						</Button>
					</DialogFooter>
				</Form>
			</DialogContent>
		</Dialog>
	);
}

export default AppointmentConfirmationDialog;
