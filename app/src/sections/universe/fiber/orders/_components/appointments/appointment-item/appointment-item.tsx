// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { formatDate, parseIri, Typography } from '@tools/reactore';
import { addMinutes } from 'date-fns';
import { capitalize, isUndefined } from 'lodash';
// api
import type { IFiberAppointment } from '@/api/interface/fiber';
import {
	EFiberAppointmentReportLabelKO,
	EFiberAppointmentReportLabelOK,
	type TFiberAppointmentReportLabelKO,
	type TFiberAppointmentReportLabelOK
} from '@/api/interface/fiber';
import { fiberQueryKeys } from '@/api/queries';
// sections
import AppointmentStateTag from '@/sections/_components/appointment-state-tag';
// components
import Tag from '@/components-old/reactore/tou-migrate/tag';

// ----------------------------------------------------------------------

type Props = {
	appointment: IFiberAppointment;
};

export default function AppointmentItem({ appointment: appointmentProps }: Props) {
	// const [, setIsModalOpen] = useState(false)
	const { state, appointmentRequests, type, comment, createdAt } = appointmentProps;

	/** Request which has a schedule with a report or if schedule is undefined */
	const request = appointmentRequests?.find((appointmentRequestItem) => {
		const appointmentSchedul = appointmentRequestItem?.appointmentSchedules?.find((appointmentSchedulesItem) => {
			return !isUndefined(appointmentSchedulesItem.appointmentReport) || isUndefined(appointmentSchedulesItem.deletedAt);
		});

		return !isUndefined(appointmentSchedul);
	});
	/** Schedule which has a report or if schedule is undefined */
	const schedule = request?.appointmentSchedules?.find((appointmentSchedulesItem) => {
		return !isUndefined(appointmentSchedulesItem.appointmentReport) || isUndefined(appointmentSchedulesItem.deletedAt);
	});
	const report = schedule?.appointmentReport;
	const documents = report?.documents || (report?.document ? [report.document] : []);

	const { mainCode, offCode, isSuccess } = report || {};

	const firstDocument = documents[0];

	const {
		data: documentDisplayData,
		isSuccess: documentDisplayIsSuccess,
		isLoading
	} = useQuery({
		...fiberQueryKeys.fiber_document.byId(firstDocument?.uuid)._ctx.display,
		enabled: Boolean(firstDocument?.uuid),
		select: (rep) => {
			let { type } = rep;

			if (firstDocument?.extension === 'pdf') {
				type = 'application/pdf';
			}

			rep = new Blob([rep], { type });

			return window.URL.createObjectURL(rep);
		}
	});

	const documentDisplayMap = new Map();
	if (documentDisplayIsSuccess && firstDocument) {
		documentDisplayMap.set(firstDocument.uuid, documentDisplayData);
	}

	let labelReport;

	if (report && !isUndefined(mainCode) && !isUndefined(offCode!)) {
		if (isSuccess) {
			labelReport = EFiberAppointmentReportLabelOK[`0${mainCode}_0${offCode}` as TFiberAppointmentReportLabelOK];
		} else {
			labelReport = EFiberAppointmentReportLabelKO[`0${mainCode}_0${offCode}` as TFiberAppointmentReportLabelKO];
		}
	}

	return (
		<>
			<article className='rounded-lg border border-gray-100 p-4 bg-gray-100 dark:bg-free-gray-800 dark:text-white'>
				<div className='flex-1'>
					<div className='mb-4 flex justify-between'>
						<div>
							<Tag className='bg-free-gray-100 text-free-gray-700'>{capitalize(type)}</Tag>
							<span className='ml-2 text-sm font-semibold'>#{parseIri(appointmentProps['@id'])}</span>

							<Typography element='span' type='caption' color='gray' className='ml-2'>
								Demandé le {formatDate(createdAt, 'dd/MM/yyyy HH:mm')}
							</Typography>
						</div>
						<AppointmentStateTag report={report} state={state} />
					</div>
					<div className='flex'>
						{schedule?.scheduledAt && (
							<div className='flex h-fit flex-col items-center rounded-lg border border-free-gray-100 bg-free-white p-2 dark:bg-free-gray-800 dark:text-white'>
								<Typography className='font-semibold'>{formatDate(schedule.scheduledAt, 'dd MMM')}</Typography>
								<Typography type='caption' color='gray' className='dark:text-white'>
									{formatDate(schedule.scheduledAt, 'yyyy')}
								</Typography>
							</div>
						)}
						<div className='mx-4 flex flex-1 flex-col items-start py-2'>
							<Typography type='caption' color='gray'>
								{schedule?.scheduledAt && `De ${formatDate(schedule.scheduledAt, 'HH:mm')}`}
								{schedule?.planedDuration &&
									schedule?.scheduledAt &&
									` a ${formatDate(addMinutes(new Date(schedule.scheduledAt), schedule?.planedDuration), 'HH:mm')}`}
							</Typography>

							<Typography className='text-sm'>{labelReport || comment}</Typography>
						</div>
					</div>
				</div>
				{documents.length > 0 && !isLoading && (
					<div className='ml-5 mt-5'>
						<div className='flex flex-wrap gap-4'>
							{firstDocument && documentDisplayIsSuccess && (
								<a className='h-full' target='_blank' href={documentDisplayData} rel='noreferrer'>
									{firstDocument.extension !== 'pdf' && (
										<div className='h-[100px] w-12'>
											<img className='h-full w-auto' alt='fiche rdv' src={documentDisplayData} />
										</div>
									)}
									{firstDocument.extension === 'pdf' && <span className='font-bold'>Débrief de l'intervention</span>}
								</a>
							)}

							{documents.length > 1 && (
								<div className='flex items-center'>
									<span className='text-sm text-free-gray-500'>
										+{documents.length - 1} document{documents.length > 2 ? 's' : ''} supplémentaire
										{documents.length > 2 ? 's' : ''}
									</span>
								</div>
							)}
						</div>
					</div>
				)}
			</article>
			{/* <UpdateAppointmentModal
				isModalOpen={isModalOpen}
				setIsModalOpen={setIsModalOpen}
				appointment={appointment}
			/> */}
		</>
	);
}
