import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { File02, InfoCircle, Rows03 } from '@tools/reactor-icons';
import { Button } from '@/components/ui/button.tsx';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppointmentCalendar from './appointment-calendar';

interface DialogProps {
	onOpenChange?: (open: boolean) => void;
	open?: boolean;
}

function BookAppointmentModal(props: DialogProps) {
	const { onOpenChange, open } = props;
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				closeColor={'#5d5d5d'}
				className='flex flex-col max-h-[calc(100vh-100px)] w-[calc(100vw-100px)] max-w-none bg-card'>
				<DialogHeader>
					<DialogTitle>Rendez-vous</DialogTitle>
				</DialogHeader>
				<div className='flex flex-col grow overflow-y-auto'>
					<Alert variant='primary'>
						<InfoCircle size={20} />
						<AlertTitle>Info RDV</AlertTitle>
						<AlertDescription>
							Le client doit être informé qu’il s’agit d’un rendez-vous de type post-production
						</AlertDescription>
					</Alert>
					<div className='mt-3 text-lg font-semibold'>Prise de rendez-vous</div>
					<Tabs defaultValue='simple' className='w-full mt-4'>
						<div className='flex w-full justify-between items-center'>
							<TabsList>
								<TabsTrigger value='simple'>
									<File02 size={18} /> Simple
								</TabsTrigger>
								<TabsTrigger value='advanced'>
									<Rows03 size={18} /> Avancé
								</TabsTrigger>
							</TabsList>
							<div className={'flex items-center gap-10'}>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Matching par</span>
									<span className='text-sm font-semibold'>NRO</span>
								</div>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Durée du RDV</span>
									<span className='text-sm font-semibold'>1h00</span>
								</div>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Forcer sur l'UPR00</span>
									<span className='text-sm font-semibold'>Non</span>
								</div>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Hors Plage Ouvrées</span>
									<span className='text-sm font-semibold'>Oui</span>
								</div>
							</div>
						</div>
						<TabsContent value='simple'>
							<AppointmentCalendar />
						</TabsContent>
						<TabsContent value='advanced'>Mode avancé ici</TabsContent>
					</Tabs>
				</div>

				<DialogFooter>
					<Button variant='outline' onClick={() => onOpenChange?.(false)}>
						Annuler
					</Button>
					<Button className='bg-purple-600 hover:bg-purple-700'>Confirmer le rendez-vous</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}

export default BookAppointmentModal;
