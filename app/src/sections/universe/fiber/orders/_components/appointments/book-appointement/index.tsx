import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { File02, InfoCircle, Rows03 } from '@tools/reactor-icons';
import { Button } from '@/components/ui/button.tsx';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppointmentCalendar from './appointment-calendar';
import AppointmentConfirmationDialog from './appointment-confirmation-dialog';

interface DialogProps {
	onOpenChange?: (open: boolean) => void;
	open?: boolean;
}

interface SelectedAppointment {
	date: string;
	time: string;
	type: string;
}

function BookAppointmentModal(props: DialogProps) {
	const { onOpenChange, open } = props;
	const [selectedAppointment, setSelectedAppointment] = useState<SelectedAppointment | null>(null);
	const [showConfirmation, setShowConfirmation] = useState(false);

	const handleSelectionChange = (selection: SelectedAppointment | null) => {
		setSelectedAppointment(selection);
	};

	const handleConfirmAppointment = () => {
		if (selectedAppointment) {
			setShowConfirmation(true);
		}
	};

	const handleAppointmentConfirmed = (data: { comment?: string }) => {
		// Here you would typically make an API call to book the appointment
		console.log('Appointment confirmed:', { ...selectedAppointment, ...data });
		setShowConfirmation(false);
		setSelectedAppointment(null);
		onOpenChange?.(false);
	};

	const getAppointmentTypeLabel = (type: string) => {
		switch (type) {
			case 'HO single':
			case 'HO':
				return 'Raccordement';
			case 'HNO single':
			case 'HNO':
				return 'Raccordement';
			default:
				return 'Raccordement';
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				closeColor={'#5d5d5d'}
				className='flex flex-col max-h-[calc(100vh-100px)] w-[calc(100vw-100px)] max-w-none bg-card'>
				<DialogHeader>
					<DialogTitle>Rendez-vous</DialogTitle>
				</DialogHeader>
				<div className='flex flex-col grow overflow-y-auto'>
					<Alert variant='primary'>
						<InfoCircle size={20} />
						<AlertTitle>Info RDV</AlertTitle>
						<AlertDescription>
							Le client doit être informé qu’il s’agit d’un rendez-vous de type post-production
						</AlertDescription>
					</Alert>
					<div className='mt-3 text-lg font-semibold'>Prise de rendez-vous</div>
					<Tabs defaultValue='simple' className='w-full mt-4'>
						<div className='flex w-full justify-between items-center'>
							<TabsList>
								<TabsTrigger value='simple'>
									<File02 size={18} /> Simple
								</TabsTrigger>
								<TabsTrigger value='advanced'>
									<Rows03 size={18} /> Avancé
								</TabsTrigger>
							</TabsList>
							<div className={'flex items-center gap-10'}>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Matching par</span>
									<span className='text-sm font-semibold'>NRO</span>
								</div>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Durée du RDV</span>
									<span className='text-sm font-semibold'>1h00</span>
								</div>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Forcer sur l'UPR00</span>
									<span className='text-sm font-semibold'>Non</span>
								</div>
								<div className='flex flex-col'>
									<span className='text-sm text-muted-foreground'>Hors Plage Ouvrées</span>
									<span className='text-sm font-semibold'>Oui</span>
								</div>
							</div>
						</div>
						<TabsContent value='simple'>
							<AppointmentCalendar onSelectionChange={handleSelectionChange} />
						</TabsContent>
						<TabsContent value='advanced'>Mode avancé ici</TabsContent>
					</Tabs>
				</div>

				<DialogFooter>
					<Button variant='outline' onClick={() => onOpenChange?.(false)}>
						Annuler
					</Button>
					<Button
						className='bg-purple-600 hover:bg-purple-700'
						onClick={handleConfirmAppointment}
						disabled={!selectedAppointment}
					>
						Confirmer le rendez-vous
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>

		{/* Appointment Confirmation Dialog */}
		<AppointmentConfirmationDialog
			open={showConfirmation}
			onOpenChange={setShowConfirmation}
			appointmentData={
				selectedAppointment
					? {
							date: selectedAppointment.date,
							time: selectedAppointment.time,
							type: getAppointmentTypeLabel(selectedAppointment.type),
							duration: '2h00',
							technician: 'Jean-jacques LA PINCE'
						}
					: null
			}
			onConfirm={handleAppointmentConfirmed}
		/>
	</>
);

export default BookAppointmentModal;
