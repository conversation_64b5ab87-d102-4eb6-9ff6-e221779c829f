import { forwardRef, type ForwardRefExoticComponent, Fragment, type RefAttributes, useState } from 'react';
// utils
import { Typography } from '@tools/reactore';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import { Badge } from '@/components/ui/badge.tsx';
import { Button } from '@/components/ui/button.tsx';
import { CalendarPlus02 } from '@tools/reactor-icons';
import AppointmentItem from '@/sections/universe/fiber/orders/_components/appointments/appointment-item';
import BookAppointmentModal from '@/sections/universe/fiber/orders/_components/appointments/book-appointement';
import { useQuery } from '@tanstack/react-query';
import { queries } from '@/api/queries';
import { add, format } from 'date-fns';

// ----------------------------------------------------------------------

const AppointmentSchedule: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef<HTMLElement>((_, ref) => {
	const { order, appointments } = useOrder();

	const [isModalOpen, setIsModalOpen] = useState(true);

	const startDate = new Date();
	const endDate = add(new Date(), { days: 7 });

	const { data: availabilities } = useQuery({
		...queries.fiber_order
			.byId(order?.['@id'])
			._ctx.planning({ duration: 60, from: format(startDate, 'yyyy-MM-dd'), to: format(endDate, 'yyyy-MM-dd') })
	});

	console.log('availabilities', availabilities);

	if (order) {
		return (
			<section ref={ref} id='appointments' className={'py-3'}>
				<div className='flex justify-between items-center mb-4'>
					<div className='flex items-center gap-2'>
						<div className='text-md font-semibold'>Rendez-vous</div>
						<Badge variant='gray' size={'sm'}>
							PROXY XXXXX
						</Badge>
						<Badge variant='gray' size={'sm'}>
							UPR00
						</Badge>
					</div>
				</div>
				<div className='flex flex-col gap-2 w-full p-4 bg-card rounded-xl mt-1 font-semibold text-lg'>
					<div className='flex flex-col w-full overflow-y-auto max-h-72'>
						{appointments?.length ? (
							appointments?.map((appointment) => {
								return (
									<Fragment key={appointment['@id']}>
										<AppointmentItem appointment={appointment} />
									</Fragment>
								);
							})
						) : (
							<div className='flex items-center justify-center h-full'>
								<img
									className='w-64 mr-40'
									src='/assets/images/fiber/order/placeholder_appointment.svg'
									alt='placeholder_appointment_img'
								/>
								<div className='flex flex-col justify-center'>
									<span>Aucun rendez-vous n’a été planifié pour le moment.</span>
								</div>
							</div>
						)}
					</div>
					<div className='flex items-center justify-end'>
						<Button variant='outline-primary' size='sm' onClick={() => setIsModalOpen(true)}>
							<CalendarPlus02 />
							Planifier
						</Button>
					</div>
				</div>
				<BookAppointmentModal open={isModalOpen} onOpenChange={setIsModalOpen} />
			</section>
		);
	}

	return (
		<section ref={ref} id='appointmentSchedule' className='col-span-1'>
			<div className='flex flex-col h-full'>
				<div className='flex items-end mb-2'>
					<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
						Rendez-vous
					</Typography>
				</div>
				<Skeleton className='h-116 rounded-2xl' />
			</div>
		</section>
	);
});
export default AppointmentSchedule;
